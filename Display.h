#ifndef DISPLAY_H
#define DISPLAY_H

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <Adafruit_GFX.h>
#include <EEPROM.h>
#include "CmdOS.h"

// Forward declarations
extern MatrixPanel_I2S_DMA *dma_display;
extern Adafruit_GFX *display;

// Display configuration structure
typedef struct {
  int pX=64;
  int pY=64;
  byte panelChain=1;
  char pins[64]="0,15,4,16,27,17,5,18,19,21,12,33,25,22";
  byte brightness=90;
  byte rotation=0;
  boolean dmaBuffer=false;
  boolean displayBuffer=true;
  byte latBlanking=1;
  boolean clkphase=true;  
  char* driver=NULL;
} eeDisplay_t;

extern eeDisplay_t eeDisplay;
extern boolean displayEnable;
extern int pixelX;
extern int pixelY;
extern byte fontSize;

// Color constants
extern int col_red;
extern int col_white;
extern int col_black;
extern int col_green;
extern int col_blue;

// Function declarations
void displaySetup();
char* displayInfo();
void displaySave();
void displayLoad();
boolean displayInit();
void displayDraw();
void displayClear();
void bufferClear();
void displayBrightness(int b);
char* displayRotation(int r);
uint16_t toColor444(int r, int g, int b);
uint16_t toColor565(int r, int g, int b);

#endif // DISPLAY_H