#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_matrixcos/CmdOS.h"
#ifndef CMDOS_H
#define CMDOS_H

#include <WiFi.h>
#include <EEPROM.h>
#include <time.h>
#include <sys/time.h>
#include <ESPAsyncWebServer.h>
#include <AnimatedGIF.h>

// Forward declarations
extern const char *prgTitle;
extern const char *prgVersion;
extern const char* user_admin;

// Mode definitions
#define EE_MODE_FIRST 0
#define EE_MODE_SETUP 1
#define EE_MODE_AP 2
#define EE_MODE_WIFI_OFF 10
#define EE_MODE_PRIVAT 20
#define EE_MODE_WIFI_TRY 21
#define EE_MODE_WIFI_CL_RECONNECT 23
#define EE_MODE_OK 30
#define EE_MODE_START 40
#define EE_MODE_WRONG 45
#define EE_MODE_ERROR 50
#define EE_MODE_SYSERROR 51

// Access levels
#define ACCESS_ADMIN 1
#define ACCESS_CHANGE 2
#define ACCESS_READ 3
#define ACCESS_ALL 4

// Log levels
#define LOG_SYSTEM 0
#define LOG_ERROR 2
#define LOG_INFO 5
#define LOG_DEBUG 10

// Configuration
#define EEBOOTSIZE 500
#define valueMax 32
#define bufferMax 500
#define paramBufferMax 128
#define maxInData 150

extern byte logLevel;
extern char* buffer;
extern char* EMPTY;
extern char* paramBuffer;
extern String EMPTYSTRING;

// Boot data structure
typedef struct {
  unsigned long timestamp=0;
  unsigned long saveCount=0;
  char wifi_ssid[32]="";
  char wifi_pas[32]="";
  char wifi_ntp[32]="";
  char espName[32];
  char espPas[32]="";
  char espBoard[32]="";
  char mqtt[128]="";
  boolean mqttLogEnable=false;
  byte accessLevel=ACCESS_ADMIN;
} eeBoot_t;

extern eeBoot_t eeBoot;
extern byte eeMode;
extern int eeAppPos;
extern char eeType[5];
extern boolean serialEnable;
extern boolean cmdEnable;
extern boolean wifiEnable;
extern boolean ntpEnable;
extern boolean webEnable;
extern boolean mdnsEnable;
extern boolean bootSafe;

// Function declarations
void cmdOSSetup();
void cmdOSLoop();
bool isAccess(int requireLevel);
void logPrintln(int level, const char *text);
void logPrintln(int level, String text);
char* setLogLevel(int level);
void espRestart(char* message);
uint32_t espChipId();
char* espInfo();
boolean isModeOk();
boolean isModeNoSystemError();
boolean isModeNoError();
void eeSave();
void eeRead();
char* bootInfo();
char* bootSet(char* espName, char* espPas, char* espBoard);
void bootSave();
void bootRead();
void bootClear();
char* bootReset(char *p);
void setAccess(boolean login);
void setAccessLevel(byte accessLevel);
boolean login(char *p);

// String utilities
char* copy(char* org);
char* copy(char *to, char* org, int max);
char* copy(char *to, String str, int max);
char* copy(String str);
void replace(char *str, char old_char, char new_char);
boolean endsWith(char *str, char *find);
boolean startWith(char *str, char *find);
char* extract(char *start, char *end, char *src);
boolean equals(char *str, char *find);
int size(char *text);
void insert(char* buffer, int pos, char* insertText);
boolean is(char *p);
boolean is(char *p, int min, int max);
boolean is(String str);
boolean is(String str, int min, int max);
char* to(byte d);
char* to(int d);
char* to(long d);
char* to(boolean d);
char* to(char *p);
const char* to(const char *p);
char* to(char *a, char *b);
char* to(const char *a, const char *b, const char *c);
char* to(const char *a, const char *b, const char *c, const char *d);
char* to(const char *a, const char *b, const char *c, const char *d, const char *e);
String toString(const char *text);
boolean toBoolean(int i);
boolean toBoolean(char *p);
int toInt(char *p);
double toDouble(char *p);
long int toLong(char *p);
unsigned long toULong(char *p);
boolean isInt(char *p);
boolean isBoolean(char *p);
char* paramAppend(char *text1, char *text2);

// Command processing
char* cmdLine(char* line);
char* cmdExec(char *cmd, char **param);
char* cmdParam(char **pp);
char* nextParam(char **pp);
void cmdParamSkip(char **pp);
char* appCmd(char *cmd, char **param);

// Application callback
extern char* (*appCmdHandler)(char *cmd, char **param);

#endif // CMDOS_H