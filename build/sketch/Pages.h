#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_matrixcos/Pages.h"
#ifndef PAGES_H
#define PAGES_H

#include "Graphics.h"
#include "CmdOS.h"

// Page class
class PageFunc { 
private: 
  void (*pageSetup)();
  void (*pageLoop)();
  const char *pageName;

public:
  void doSetup() {  if(pageSetup!=NULL) { pageSetup(); } }
  void doLoop() {  if(pageLoop!=NULL) { pageLoop(); } }
  char* toString() { return (char*)pageName; }

  PageFunc() { pageSetup = NULL; pageLoop = NULL;  pageName=NULL; }
  PageFunc(const char *name,void (*pSetup)(),void (*pLoop)()) {
    pageName=name; pageSetup=pSetup; pageLoop=pLoop;
  }
}; 

// Page management
extern PageFunc* pageFunc;
extern byte pageIndex;
extern int pageRefresh;

// Function declarations
void pageSetup();
void pageLoop();
void pageClear();
byte pageSet(int page);
byte pageChange(int pageAdd);
char* pageList();
char* pageDel(int pageIndex);

// Page implementations
void pageTitle();
void pageStart();
void pageEsp();
void pageTime();
void pageGif();
void pageCmd();

void displayPageSetup();
void displayPageLoop();

#endif // PAGES_H