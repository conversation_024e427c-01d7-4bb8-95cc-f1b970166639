#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_matrixcos/CmdOS.cpp"
#include "CmdOS.h"

// Global variables
const char *prgTitle = "MatrixCOS";
const char *prgVersion = "V1.2.2";
const char* user_admin = "admin";
char user_pas[] = "";
const char *wifi_ssid_default = "";
const char *wifi_pas_default = "";
const char *mqtt_default = "";
byte MODE_DEFAULT = 21;

boolean serialEnable = true;
boolean cmdEnable = true;
boolean wifiEnable = true;
boolean ntpEnable = true;
boolean webEnable = true;
boolean mdnsEnable = true;
boolean bootSafe = false;

byte logLevel = LOG_INFO;
static char* buffer = new char[bufferMax];
static char* EMPTY = "";
static char* paramBuffer = new char[paramBufferMax];
String EMPTYSTRING = "";

char eeType[5];
byte eeMode = 0;
int eeAppPos = 0;
eeBoot_t eeBoot;

// Function pointer for application command handler
char* (*appCmdHandler)(char *cmd, char **param) = nullptr;

// Utility functions
char* copy(char* org) { 
  if(org==NULL) { return NULL; }
  int len=strlen(org);
  char* newStr=new char[len+1]; 
  memcpy( newStr, org, len); newStr[len]='\0'; 
  return newStr;
}

char* copy(char *to,char* org,int max) { 
  if(to==NULL) { to=new char[max+1]; }
  if(to==NULL) { espRestart("copy() memory error"); }
  if(org!=NULL) { 
    int len=strlen(org); if(len>max) { len=max; }
    memcpy( to, org, len); to[len]='\0'; 
  }else { to[0]='\0'; }
  return to;
}

boolean equals(char *str,char *find) {
  if(!is(str) || !is(find)) { return false; }
  int l1=strlen(str); int l2=strlen(find);
  if(l1!=l2) { return false; }
  for(int i=0;i<l2;i++) {  
    if(*str!=*find) { return false; } 
    str++; find++;
  }
  return true;
}

boolean is(char *p) { return p!=NULL && p!=EMPTY; }
boolean is(char *p,int min,int max) { return p!=NULL && strlen(p)>=min && strlen(p)<max; }

char* to(int d) { sprintf(buffer,"%d",d); return buffer; }
char* to(char *p) {if(p!=NULL && strlen(p)>0 && strlen(p)<bufferMax) { return p; } else { return EMPTY; } }

boolean toBoolean(char *p) { return p!=NULL && strlen(p)>0 && (strcmp(p, "on")==0 || strcmp(p, "true")==0 || atoi(p)>0); }
int toInt(char *p) { if(p!=NULL && strlen(p)>0) { return atoi(p); } else { return -1; } }

void espRestart(char* message) {  
  if(serialEnable) { Serial.print("### Rebooting "); Serial.println(message); delay(1000); }
  ESP.restart();
}

uint32_t espChipId() {
    uint32_t chipId=0;
    for (int i = 0; i < 17; i = i + 8) { chipId |= ((ESP.getEfuseMac() >> (40 - i)) & 0xff) << i;}
    return chipId;
}

char* espInfo() {
    sprintf(buffer,"ESP chip:%d free:%d core:%s freq:%d flashChipId:%d flashSize:%d flashSpeed:%d SketchSize:%d FreeSketchSpace:%d",    
      espChipId(),ESP.getFreeHeap(), ESP.getSdkVersion(),ESP.getCpuFreqMHz()
      ,ESP.getFlashChipMode(),ESP.getFlashChipSize(),ESP.getFlashChipSpeed(),ESP.getSketchSize(),ESP.getFreeSketchSpace());           
    return buffer;
}

void logPrintln(int level,const char *text) { 
  if(level>logLevel || !is((char*)text)) { return ; }
  if(serialEnable) { Serial.println(text); } 
}

void logPrintln(int level,String text) {  
  if(level>logLevel || !is(text)) { return ; } 
  const char* log=text.c_str();
  if(serialEnable) { Serial.println(log); } 
}

char* setLogLevel(int level) {
  if(level>=0) { logLevel=level; }
  sprintf(buffer,"%d",logLevel); return buffer;
}

boolean isModeOk() { return eeMode>EE_MODE_AP && eeMode<EE_MODE_ERROR; }
boolean isModeNoSystemError() { return eeMode<EE_MODE_SYSERROR; }
boolean isModeNoError() { return eeMode<EE_MODE_ERROR; }

void eeSave() {
  if(serialEnable) { Serial.println("### SAVE");}
  eeBoot.timestamp=millis()/1000;  
  eeBoot.saveCount++;
  int pos=0;
  EEPROM.begin(EEBOOTSIZE);
  EEPROM.put(pos, "Os01" ); pos+=5;
  EEPROM.put(pos, eeMode); pos+=1; 
  EEPROM.put(pos, eeBoot); pos+=sizeof(eeBoot);
  EEPROM.commit();
  eeAppPos=pos;
}

void eeRead() {   
  EEPROM.begin(EEBOOTSIZE);
  int pos=0;
  EEPROM.get( pos, eeType );  pos+=5;
  if(strcmp(eeType,"Os01")!=0) { eeMode=EE_MODE_FIRST; return ; }

  EEPROM.get(pos, eeMode ); pos+=1;
  EEPROM.get(pos, eeBoot); pos+=sizeof(eeBoot);
  EEPROM.end(); 
  eeAppPos=pos;
}

char* bootInfo() {
   sprintf(buffer,"eeBoot eeMode:%d espName:%s espPas:%d espBoard:%s wifi_ssid:%s mqtt:%s ntp:%s timestamp:%d count:%d", 
    eeMode, to(eeBoot.espName),is(eeBoot.espPas),to(eeBoot.espBoard),to(eeBoot.wifi_ssid),to(eeBoot.mqtt),to(eeBoot.wifi_ntp),
    eeBoot.timestamp,eeBoot.saveCount); 
   return buffer;
}

char* bootSet(char* espName,char* espPas,char* espBoard) {
  if(is(espName,1,31)) { strcpy(eeBoot.espName,espName); }
  if(is(espPas,1,31)) { strcpy(eeBoot.espPas,espPas); }
  if(is(espBoard,1,31)) { strcpy(eeBoot.espBoard,espBoard); }
  return bootInfo();
}

void bootSave() {
  eeSave();
  logPrintln(LOG_SYSTEM,bootInfo());
  espRestart("EEPROM boot save");
}

void bootRead() {
  eeRead();
  if(strcmp(eeType,"Os01")!=0) { 
    sprintf(buffer,"EEPROM wrong"); logPrintln(LOG_SYSTEM,buffer);
    return ; 
  }    
  sprintf(buffer,"EEPROM boot read mode:%d timestamp:%d espName:%s wifi_ssid:%s",eeMode,eeBoot.timestamp,eeBoot.espName,eeBoot.wifi_ssid); 
  logPrintln(LOG_SYSTEM,buffer); 
  logPrintln(LOG_SYSTEM,bootInfo());
}

void bootClear() {
  logPrintln(LOG_SYSTEM,"EEPROM boot clear");
  EEPROM.begin(EEBOOTSIZE);
  for (int i = 0 ; i < EEBOOTSIZE ; i++) {EEPROM.write(i, 0);}
  delay(200);
  EEPROM.commit();
  EEPROM.end();  
}

char* bootReset(char *p) {
  static byte _bootRestVal=0;
  int i=toInt(p);
  if(i>1 && i==_bootRestVal) { bootClear(); return "reset done";} 
  else { _bootRestVal=random(2,99); sprintf(buffer,"%d",_bootRestVal); return buffer; }
}

static boolean _isLogin=false;
bool isAccess(int requireLevel) {   
  if(_isLogin) { return true; }
  else if(!is(eeBoot.espPas)) { return true; }
  else if(requireLevel>=eeBoot.accessLevel) { return true; }
  else { 
    sprintf(buffer,"ACCESS DENIED %d<%d %d",requireLevel,eeBoot.accessLevel,_isLogin); 
    logPrintln(LOG_ERROR,buffer);     
    return false;     
  }
}

void setAccess(boolean login) { _isLogin=login; }
void setAccessLevel(byte accessLevel) { eeBoot.accessLevel=accessLevel; }

boolean login(char *p) {
  if(!is(eeBoot.espPas) || equals(p, eeBoot.espPas))  {  _isLogin=true; return true; }
  else { _isLogin=false; return false; }
}

// Command processing
char* nextParam(char **pp) {
    if(pp==NULL || *pp==NULL || **pp=='\0') { return EMPTY; }
    while(**pp==' ' || **pp=='\t' || **pp=='}') { (*pp)++; }
    if(pp==NULL || *pp==NULL || **pp=='\0') { return EMPTY; }
    char* cmd = strtok_r(NULL, " ",pp); 
    if(cmd==NULL) { return EMPTY; } else { return cmd; }
}

char* cmdParam(char **pp) {
    if(pp==NULL || *pp==NULL || **pp=='\0') { return EMPTY; }
    while(**pp==' ' || **pp=='\t') { (*pp)++; }
    if(pp==NULL || *pp==NULL || **pp=='\0') { return EMPTY; }

    char* p1;    
    if(**pp=='"') {
      (*pp)++;
      p1 = strtok_r(NULL, "\"",pp);  
      if(p1==NULL) { return EMPTY; }   
      return p1;   
    } else { 
      p1 = strtok_r(NULL, " ",pp);            
    }
    if(p1==NULL) { return EMPTY; } 
    return p1;
}

char* cmdExec(char *cmd, char **param) {  
  if(!is(cmd)) { return EMPTY; } 
  sprintf(buffer,"->%s %s",cmd,to(*param)); logPrintln(LOG_DEBUG,buffer); 

  char *ret= EMPTY;
  if(equals(cmd, "?")) { ret="CmdOS commands available"; }
  else if(equals(cmd, "esp")) { ret=espInfo();  }
  else if(equals(cmd, "stat")) { 
    sprintf(buffer,"AppInfo %s %s CmdOs bootType:%s login:%d access_level:%d",
      prgTitle,prgVersion,"Os01",_isLogin,eeBoot.accessLevel); 
    ret=buffer;
  }
  else if(equals(cmd, "freeHeap")) { sprintf(buffer,"%d",ESP.getFreeHeap());ret=buffer; }
  else if(equals(cmd, "login")) { login(cmdParam(param)); }
  else if(equals(cmd, "restart")) { espRestart("cmd restart");  }
  else if(equals(cmd, "logLevel")) { ret=setLogLevel(toInt(cmdParam(param))); }
  else if(equals(cmd, "save")) { bootSave();  }
  else if(equals(cmd, "load")) { bootRead(); }
  else if(equals(cmd, "conf")) {  ret=bootSet(cmdParam(param),cmdParam(param),cmdParam(param)); }
  else if(equals(cmd, "reset")) { ret=bootReset(cmdParam(param)); }
  else if(appCmdHandler) { ret=appCmdHandler(cmd,param); }
  else { ret=cmd; }

  logPrintln(LOG_DEBUG,ret);
  return ret;
}

char* cmdLine(char* line) {  
  char* cmd=nextParam(&line);
  return cmdExec(cmd,&line);
}

// Serial command reading
static char inData[maxInData];
static char inIndex = 0;

void cmdRead() {
    if (Serial.available() > 0) {
    char c = Serial.read();
    if (c != '\n' && c != '\r' && inIndex < maxInData-1) { inData[inIndex++] = c; }
    else {
      inData[inIndex++] = '\0';
      String ret=cmdLine(inData);
      if(logLevel!=LOG_DEBUG) { logPrintln(LOG_SYSTEM,ret.c_str()); }      
      inIndex = 0;
    }
  }
}

void cmdOSSetup() {
  if(serialEnable) { 
    delay(1); Serial.begin(115200); 
    delay(1); Serial.println("----------------------------------------------------------------------------------------------------------------------");
  }
  
  // Initialize EEPROM default values
  uint32_t chipid=espChipId();
  if(!is(eeBoot.espName)) { snprintf(eeBoot.espName,20, "OpenOs%08X",chipid);  }
  if(!is(eeBoot.espPas)) { sprintf(eeBoot.espPas,user_pas); }
  if(!is(eeBoot.wifi_ssid)) {sprintf(eeBoot.wifi_ssid,wifi_ssid_default); } 
  if(!is(eeBoot.wifi_pas)) {sprintf(eeBoot.wifi_pas,wifi_pas_default); }
  if(!is(eeBoot.mqtt)) {sprintf(eeBoot.mqtt,mqtt_default); }
  
  eeRead();
  if(strcmp(eeType,"Os01")!=0) {
    eeMode=EE_MODE_SETUP; 
    eeSave();
  }
}

void cmdOSLoop() {
  if(serialEnable) { cmdRead(); }
  delay(0);
}