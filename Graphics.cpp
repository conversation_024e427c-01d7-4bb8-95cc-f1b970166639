#include "Graphics.h"

// Global variables
uint16_t _color;
byte _effectType = 0;
int _effectSpeed = 100;
int _effectStep = 0;
int _effectIndex = 0;
int _effectA = 1;
int _effectB = 0;
unsigned long *effectTimer = new unsigned long(0);

AnimatedGIF gif;
File f;
int x_offset, y_offset;
boolean _playGif = false;
boolean _playStart = false;
int frames = 0;

#define DEG2RAD 0.0174532925

void draw() {
  displayDraw();
}

void drawClear() {   
  if(!_displaySetup) { return ; }
  if(eeDisplay.displayBuffer) { bufferClear(); } else { displayClear(); }
} 

void drawColor(uint16_t color) { _color=color; }

void fillScreen(int color) { 
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  display->fillScreen(color);  
} 

void drawPixel(uint16_t x, uint16_t y, uint16_t color) { 
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  display->drawPixel(x,y, color); 
}  

void drawLine(int x,int y, int w,int h,int color) { 
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  display->drawLine(x,y,w,h, color); 
}    

void drawRect(int x,int y, int w,int h,int color) {
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  display->drawRect(x,y,w,h, color); 
}   

void fillRect(int x,int y, int w,int h,int color) { 
  if(!_displaySetup) { return ; }
  if(color==-1) { color=_color; } 
  display->fillRect(x,y,w,h, color); 
}  

void drawLine(int x,int y, int w,int h, int xw,int yw, int color) { 
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  for(int i=0;i<xw;i++) {  
    for(int t=0;t<yw;t++) { 
      display->drawLine(x+i,y+t,w+i,h+t, color); 
    }
  }
}
 
void drawTriangle(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color) {
  if(!_displaySetup) { return ; }
  if(color==-1) { color=_color; } 
  display->drawTriangle(x0,y0,x1,y1,x2,y2,color);
}

void fillTriangle(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color) {
  if(!_displaySetup) { return ; }
  if(color==-1) { color=_color; } 
  display->fillTriangle(x0,y0,x1,y1,x2,y2,color);
}

void drawCircle(int x,int y, int w,int color) { 
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  display->drawCircle(x,y,w, color); 
}   

void fillCircle(int x,int y, int w,int color) { 
  if(!_displaySetup) { return ; } 
  if(color==-1) { color=_color; } 
  display->fillCircle(x,y,w, color); 
}  

void drawRoundRect(uint16_t x0, uint16_t y0, uint16_t w, uint16_t h, uint16_t radius, uint16_t color) { 
  if(!_displaySetup) { return ; }
  if(color==-1) { color=_color; } 
  display->drawRoundRect(x0,y0,w,h,radius,color); 
}

void fillRoundRect(uint16_t x0, uint16_t y0, uint16_t w, uint16_t h, uint16_t radius, uint16_t color) { 
  if(!_displaySetup) { return ; }
  if(color==-1) { color=_color; } 
  display->fillRoundRect(x0,y0,w,h,radius,color);  
}

void drawChar(uint16_t x, uint16_t y, char c, uint16_t color, uint16_t bg, uint8_t size) { 
  if(!_displaySetup) { return ; }
  if(color<0) { color=_color; } 
  display->drawChar(x,y,c,color,bg,size); 
}

void drawText(int x,int y, int size, const char *str,int color) {
  if(!_displaySetup) { return ; }
  if(color<0) { color=_color; }
  display->setTextSize(size);
  display->setTextWrap(true);
  display->setCursor(x,y);
  display->setTextColor(color);
  display->print(str);  
}

void getTextBounds(const char *text,uint16_t *w,uint16_t *h) {
  int16_t  x1, y1; 
  display->getTextBounds(text, 0, 0, &x1, &y1, w, h);
}

void drawBitmap(int16_t x, int16_t y, uint8_t *bitmap, int16_t w, int16_t h, uint16_t color) {
  if(!_displaySetup) { return ; }
  if(color<0) { color=_color; } 
  display->drawBitmap(x,y,bitmap,w,h,color);
}

void drawArc(int x, int y, int segmentStep, int seg_start, int seg_count, int rx, int ry, int w, int color) {
  int segmentInc=segmentStep;
  int start_angle=seg_start*segmentInc;
  if(!_displaySetup) { return ; }
  if(color==-1) { color=_color; }  
  float sx = cos((start_angle - 90) * DEG2RAD);
  float sy = sin((start_angle - 90) * DEG2RAD);
  uint16_t x0 = sx * (rx - w) + x;
  uint16_t y0 = sy * (ry - w) + y;
  uint16_t x1 = sx * rx + x;
  uint16_t y1 = sy * ry + y;

  for (int i = start_angle; i < start_angle + segmentStep * seg_count; i += segmentInc) {
    float sx2 = cos((i + segmentStep - 90) * DEG2RAD);
    float sy2 = sin((i + segmentStep - 90) * DEG2RAD);
    int x2 = sx2 * (rx - w) + x;
    int y2 = sy2 * (ry - w) + y;
    int x3 = sx2 * rx + x;
    int y3 = sy2 * ry + y;
    display->fillTriangle(x0, y0, x1, y1, x2, y2, color);
    display->fillTriangle(x1, y1, x2, y2, x3, y3, color);
    x0 = x2;
    y0 = y2;
    x1 = x3;
    y1 = y3;  
  }
}

void drawFull(int x,int y,int w,int h,int p,int value,int max,int col1,int col2) {
  if(col1==-1) { col1=_color; }  
  if(col2==-1) { col2=_color; }  
  drawRect(x,y,w,h,col1);  
  if(w>h) { 
    float step=(float)w/(float)max; 
    int w2=(int)((float)value*step);
    if(w2>p*2) {fillRect(x+p,y+p,w2-(p*2),h-(p*2),col2); }
  } else { 
    float step=(float)h/(float)max; 
    int w2=(int)((float)value*step);
    if(w2>p*2) { fillRect(x+p,y+p+h-w2, w-(p*2),w2-(p*2),col2); }
  } 
}

void drawGauge(int x,int y,int w,int p,int value,int max,int col1,int col2) {
  if(col1==-1) { col1=_color; } 
  drawArc(x,y, 1, 270, 180, w,w, 0, col1);
  drawLine(x-w,y,x+w,y,col1);
  int v=(180/max)*value;
  drawArc(x,y,1,270+v,2,w-p,w-p,w,col2);
}

void drawOn(int x,int y,int w,int p,boolean on,int col1,int col2) {
  drawCircle(x,y,w,col1);
  if(on) { fillCircle(x,y,w-p,col2); }
}

void drawValue(int x,int y,char *text,int value,int max,int col1,int col2) {
  drawText(x,y,1,text,_color);
  drawText(x,y+8,1,to(value),col1);
  drawLine(x,y+16,x+63,y+16,_color);
}

void valueFull(int x,int y,char *text,int value,int max,int col1,int col2) {
  drawValue(x,y,text,value,max,col1,col2);
  if(value<0) { col1=col2; value=value*-1; } 
  drawFull(x+40,y,20,8,1,value,max,_color,col1);
}

void valueOn(int x,int y,char *text,int value,int max,int col1,int col2) {
  drawValue(x,y,text,value,max,col1,col2);
  if(value>max) { col1=col2; }
  drawOn(x+53,y+7,5,1,true,_color,col1);
}

void valueGauge(int x,int y,char *text,int value,int max,int col1,int col2) {
  drawValue(x,y,text,value,max,col1,col2);
  if(value>max) { col1=col2; }
  drawGauge(x+50,y+10,10,1,value,max,_color,col1);
}

void drawTime(int x,int y,int size,int color) {  
  // This would need time functions from CmdOS
  drawText(x,y,size,"--:--:--",color);
}

void drawDate(int x,int y,int size,int color) {  
  // This would need date functions from CmdOS
  drawText(x,y,size,"--.--.----",color);
}

// Effect functions
void effectShift() {
    for(int x=0;x<pixelX;x++) {
      for(int y=0;y<pixelY;y++) {
        GFXcanvas16 *canvas=(GFXcanvas16*)display;
        int px=x, py=y; uint16_t color=0;
        if(_effectA<0) { px=pixelX-x-1; }        
        if(_effectB<0) { py=pixelY-y-1; }
        int posX, posY;
        if(_effectA>=5) { posX=px+random(1,_effectA); }        
        else if(_effectA<=-5) { posX=px-random(1,(_effectA*-1)); }    
        else { posX=px+_effectA; }
        if(_effectB>=5) { posY=py+random(1,_effectB); } 
        else if(_effectB<=-5) { posY=py-random(1,(_effectB*-1)); } 
        else { posY=py+_effectB; }
        if(posX>=0 && posX<pixelX && posY>=0 && posY<pixelY) { 
          color= canvas->getPixel(posX,posY); 
        }
        canvas->drawPixel(px,py,color);
      }
    }
    draw();
}

void effectLoop() {
  if(eeDisplay.displayBuffer && _effectType!=0 && _effectIndex<_effectStep) { 
    // Check timer here if needed
    if(_effectType==1) { effectShift();  }
    _effectIndex++;
    if(_effectIndex==_effectStep) { 
      logPrintln(LOG_DEBUG, "effect end"); 
    }
  }
}

void effectStart(byte effectType,int effectStep,int effectSpeed,int effectA,int effectB) {
  sprintf(buffer,"effect start %d %d %d %d %d",effectType,effectStep,effectSpeed,effectA,effectB); 
  logPrintln(LOG_DEBUG,buffer);
  _effectType=effectType;
  _effectStep=effectStep; 
  _effectSpeed=effectSpeed;
  _effectA=effectA; 
  _effectB=effectB;
  _effectIndex=0;
}

// Simplified file system functions (these would need full SPIFFS implementation)
boolean fsDelete(String file) { return SPIFFS.remove(file); }
boolean fsWrite(String file, char *p1) { 
  File ff = SPIFFS.open(file, FILE_WRITE);
  if(!ff){ return false; }
  if(p1!=NULL && strlen(p1)>0) { ff.print(p1); }
  ff.close();
  return true;
}

char* fsRead(String file) {  
  File ff = SPIFFS.open(file, FILE_READ);  
  if(!ff) { return NULL; } 
  size_t fileSize= ff.size();
  char *charArray = new char[fileSize + 1];
  ff.readBytes(charArray, fileSize);
  charArray[fileSize] = '\0';
  ff.close();
  return charArray;
}

int fsSize(String file) { 
  File ff = SPIFFS.open(file);
  if(!ff) { return -1; } 
  int len=ff.size();
  ff.close();
  return len;
}

// Simplified GIF and drawing functions
void drawLoop() {
  // GIF playback logic would go here
}

void drawFileClose() {
  _playStart=false;
  _playGif=false;
  // gif.close() would go here
}

void drawStop() {
  drawFileClose();
}

void drawClose() {
  drawStop();
  drawClear();
  draw(); 
}

void drawOff() {
  drawClose();
}