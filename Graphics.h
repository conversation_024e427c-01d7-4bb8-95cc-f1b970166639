#ifndef GRAPHICS_H
#define GRAPHICS_H

#include "Display.h"
#include <AnimatedGIF.h>
#include <SPIFFS.h>

// Drawing function declarations
void draw();
void drawClear();
void drawColor(uint16_t color);
void fillScreen(int color);
void drawPixel(uint16_t x, uint16_t y, uint16_t color);
void drawLine(int x, int y, int w, int h, int color);
void drawLine(int x, int y, int w, int h, int xw, int yw, int color);
void drawRect(int x, int y, int w, int h, int color);
void fillRect(int x, int y, int w, int h, int color);
void drawTriangle(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);
void fillTriangle(uint16_t x0, uint16_t y0, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);
void drawCircle(int x, int y, int w, int color);
void fillCircle(int x, int y, int w, int color);
void drawRoundRect(uint16_t x0, uint16_t y0, uint16_t w, uint16_t h, uint16_t radius, uint16_t color);
void fillRoundRect(uint16_t x0, uint16_t y0, uint16_t w, uint16_t h, uint16_t radius, uint16_t color);
void drawChar(uint16_t x, uint16_t y, char c, uint16_t color, uint16_t bg, uint8_t size);
void drawText(int x, int y, int size, const char *str, int color);
void getTextBounds(const char *text, uint16_t *w, uint16_t *h);
void drawBitmap(int16_t x, int16_t y, uint8_t *bitmap, int16_t w, int16_t h, uint16_t color);

// Advanced drawing functions
void drawArc(int x, int y, int segmentStep, int seg_start, int seg_count, int rx, int ry, int w, int color);
void drawFull(int x, int y, int w, int h, int p, int value, int max, int col1, int col2);
void drawGauge(int x, int y, int w, int p, int value, int max, int col1, int col2);
void drawOn(int x, int y, int w, int p, boolean on, int col1, int col2);

// Value display functions
void drawValue(int x, int y, char *text, int value, int max, int col1, int col2);
void valueFull(int x, int y, char *text, int value, int max, int col1, int col2);
void valueOn(int x, int y, char *text, int value, int max, int col1, int col2);
void valueGauge(int x, int y, char *text, int value, int max, int col1, int col2);

// Time/Date functions
void drawTime(int x, int y, int size, int color);
void drawDate(int x, int y, int size, int color);

// Icon and file functions
void drawIcon(int x, int y, int w, int h, int color, char *xbm, int size);
boolean drawIcon(int x, int y, int w, int h, int color, char *name);
void drawFile(char *name, char *suffix, int x, int y, boolean direct);
void drawUrl(String url, int x, int y, boolean direct);

// File system functions
boolean fsDelete(String file);
boolean fsWrite(String file, char *p1);
boolean fsWriteBin(String file, uint8_t *p1, int len);
char* fsRead(String file);
uint8_t* fsReadBin(String file, size_t& fileSize);
int fsSize(String file);
char* fsDir(String find);
int fsDirSize(String find);
char* fsFile(String find, int count, int type);

// Effects
void effectStart(byte effectType, int effectStep, int effectSpeed, int effectA, int effectB);
void effectLoop();

// GIF support
extern AnimatedGIF gif;
void drawLoop();
void drawFileClose();
void drawStop();
void drawClose();
void drawOff();

#endif // GRAPHICS_H