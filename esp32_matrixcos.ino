#include "CmdOS.h"
#include "Display.h"
#include "Graphics.h"
#include "Pages.h"

// Application command handler
char* matrixCmd(char *cmd, char **param);

void setup() {
  // Set the application command handler
  appCmdHandler = matrixCmd;
  
  // Initialize CmdOS
  cmdOSSetup();
  
  if(isModeNoError()) { 
    displaySetup();
    pageSetup();
    displayPageSetup();
  }  
}

void loop() {
  cmdOSLoop();
  
  if(isModeNoError()) { 
    drawLoop();
    pageLoop();
    displayPageLoop();
    effectLoop();
  }  
}

// Application-specific command handler
char* matrixCmd(char *cmd, char **param) {
    // drawOff => switch display off by clear and stop all
    if(equals(cmd, "drawOff")) { drawOff(); return EMPTY; }
    // drawClear => clear display
    else if(equals(cmd, "drawClear")) { drawClear(); return EMPTY; } 
    // drawStop => stop all (not clear)
    else if(equals(cmd, "drawStop")) { drawStop(); return EMPTY; } 
    // draw => draw buffer, draw content from buffer on display or flip dma buffer
    else if(equals(cmd, "draw")) { draw(); return EMPTY; } 

    // brightness n - set up brightness of display
    else if(equals(cmd, "brightness")) { 
      int b=toInt(cmdParam(param)); 
      displayBrightness(b); 
      return EMPTY; 
    }
    else if(equals(cmd, "rotation")) { 
      int b=toInt(cmdParam(param)); 
      return displayRotation(b); 
    }

    //drawColor r g b - calculate 444 color and set as default color    
    else if(equals(cmd, "drawColor")) { 
      uint16_t col=toColor444(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)));
      drawColor(col); 
      sprintf(buffer,"%d",col); 
      return buffer; 
    }
    // drawColor565 r g b - calculate 565 color and set as default color 
    else if(equals(cmd, "drawColor565")) { 
      uint16_t col=toColor565(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      drawColor(col); 
      sprintf(buffer,"%d",col); 
      return buffer; 
    }

    //fillScreen c - fill screen with color	
    else if(equals(cmd, "fillScreen")) { 
      fillScreen(toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // drawPixel x y c - draw a pixel at x y
    else if(equals(cmd, "drawPixel")) { 
      drawPixel(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // drawLine x y x2 y2 c - draw a line (from x,y to x2,y2)
    else if(equals(cmd, "drawLine")) { 
      drawLine(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // rawRect x y w h c - draw rect (rect from x,y to x+w,y+h)
    else if(equals(cmd, "drawRect")) { 
      drawRect(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // fillRect x y w h c - draw a filled rect
    else if(equals(cmd, "fillRect")) { 
      fillRect(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // drawTriangle x y x2 y2 x3 y3 c - draw a triangle
    else if(equals(cmd, "drawTriangle")) { 
      drawTriangle(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // fillTriangle x y x2 y2 x3 y3 c -  draw a filled triangle
    else if(equals(cmd, "fillTriangle")) { 
      fillTriangle(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }

    // drawCircle x y w c - draw circle at x y with radius w 
    else if(equals(cmd, "drawCircle")) { 
      drawCircle(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // fillCircle x y w c - draw filled circle at x y with radius w 
    else if(equals(cmd, "fillCircle")) { 
      fillCircle(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    
    // drawFull x y w h p value max, c1,c2 - draw a full-element
    else if(equals(cmd, "drawFull")) { 
      drawFull(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    else if(equals(cmd, "drawOn")) { 
      drawOn(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toBoolean(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    else if(equals(cmd, "drawGauge")) { 
      drawGauge(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }

    // drawText x y c size text - draw text at x y with size 
    else if(equals(cmd, "drawText")) { 
      drawText(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),cmdParam(param),toInt(cmdParam(param))); 
      return EMPTY; 
    }

    // set page
    else if(equals(cmd, "page")) { 
      int p=pageSet(toInt(cmdParam(param))); 
      sprintf(buffer,"%d",p); 
      return buffer; 
    }
    // next page
    else if(equals(cmd, "pagePriv")) { 
      int p=pageChange(-1);
      sprintf(buffer,"%d",p); 
      return buffer; 
    }
    // next page
    else if(equals(cmd, "pageNext")) {  
      int p=pageChange(1);
      sprintf(buffer,"%d",p); 
      return buffer; 
    }

    // set page
    else if(equals(cmd, "pages")) { return pageList();  }
    else if(equals(cmd, "pageDel")) { return pageDel(toInt(cmdParam(param)));  }

    // drawTime x y c - draw time at x,y 
    else if(equals(cmd, "drawTime")) { 
      drawTime(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }
    // drawDate x y c - draw date at x,y
    else if(equals(cmd, "drawDate")) { 
      drawDate(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY; 
    }

    // effect type step speed a b - start effect
    else if(equals(cmd, "effect")) { 
      effectStart(toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param)),toInt(cmdParam(param))); 
      return EMPTY;  
    }

    // matrix sizeX sizeY chain brightness rotation pins - config hub75 display/connection
    else if(equals(cmd, "matrix")) { 
      if(is(cmdParam(param))) {
        eeDisplay.pX=toInt(cmdParam(param));
        eeDisplay.pY=toInt(cmdParam(param));
        eeDisplay.panelChain=toInt(cmdParam(param));
        eeDisplay.brightness=toInt(cmdParam(param));
        eeDisplay.rotation=toInt(cmdParam(param));
        char* pins = cmdParam(param);
        if(is(pins)) {
          strncpy(eeDisplay.pins, pins, sizeof(eeDisplay.pins)-1);
        }
        displaySave();
      }
      return displayInfo();
    }

    else { return cmd; }
}