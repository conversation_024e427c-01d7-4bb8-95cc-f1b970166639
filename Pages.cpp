#include "Pages.h"

// Global variables
PageFunc* pageFunc = NULL;
byte pageIndex = 0;
unsigned long *pageRefreshTime = new unsigned long(0);
unsigned long *pageLoopTime = new unsigned long(0);
int pageLoopValue = 0;
int pageRefresh = 60000;

// Simple list implementation for pages
PageFunc* pages[10];
int pageCount = 0;

void pageClear() {
  if(eeDisplay.displayBuffer) {  bufferClear(); } else { displayClear(); }
  _effectType=0;
}

byte pageSet(int page) {
  if(page>=0) { 
    pageIndex=page;
    *pageRefreshTime=0;
    if(pageIndex==0) { pageFunc=NULL; } 
    else if(pageIndex-1 < pageCount) { pageFunc=pages[pageIndex-1]; }
    sprintf(buffer,"set page:%d pageRefreshTime:%d ",pageIndex,*pageRefreshTime);
    logPrintln(LOG_DEBUG,buffer);
  } 
  return pageIndex;
}

byte pageChange(int pageAdd) {
  byte page=pageIndex+pageAdd;
  if(page<1) { page=pageCount; }
  if(page>pageCount) { page=1; } 
  return pageSet(page);
}

char* pageList() {
  sprintf(buffer,"");
  for(int i=0;i<pageCount;i++) {  
    PageFunc* pf = pages[i];
    if(pf!=NULL) {
      sprintf(buffer+strlen(buffer),"page %i %s\n",i,pf->toString());
    }
  }
  return buffer;
}

char* pageDel(int index) {
  if(index >= 0 && index < pageCount) {
    delete pages[index];
    for(int i=index; i<pageCount-1; i++) {
      pages[i] = pages[i+1];
    }
    pageCount--;
  }
  return EMPTY;
}

void pageSetup() {}

// Simple timer check function
boolean isTimer(unsigned long *lastTime, unsigned long period) {
  unsigned long currentTime = millis();
  if(*lastTime == 0 || (currentTime - *lastTime) >= period) {
    *lastTime = currentTime;
    return true;
  }
  return false;
}

void pageLoop() {
  if(!displayEnable || !_displaySetup) { return ; }
  if(pageIndex>0 && isTimer(pageRefreshTime, pageRefresh)) {
    if(pageFunc!=NULL) { 
      sprintf(buffer,"page setup %d",pageIndex);
      logPrintln(LOG_DEBUG,buffer);    
      pageFunc->doSetup(); 
    }else {
      sprintf(buffer,"no page found %d",pageIndex);
      logPrintln(LOG_ERROR,buffer);   
    }  
  } else {
    if(pageFunc!=NULL) { pageFunc->doLoop(); }
  }
}

// Page implementations
void pageTitle() {
  pageRefresh=5000;
  pageClear();

  int wh=pixelX/2, hh=pixelY/2;
  long val=0; // Would get WiFi RSSI if available

  uint16_t w=30,h=30;
  int x=wh,y=50;
  drawArc(x, y, 3, 90, 60, w, h, 3, col_red);
  drawArc(x, y, 3, 90, 60, w-10, h-10, 3, col_red);
  drawArc(x, y, 3, 90, 60, w-20, h-20, 3, col_red);  
  drawArc(x, y ,3, 90, val, w, h, 3, col_green);
  drawArc(x, y, 3, 90, val, w-10, h-10, 3, col_green);
  drawArc(x, y, 3, 90, val, w-20, h-20, 3, col_green);

  fillTriangle(x, y, x-w, y-h, x-w, y, col_black);
  fillTriangle(x, y, x+w, y-h, x+w, y, col_black);
  fillCircle(x, y+10, 3, col_red);

  // title
  uint16_t tw, th;
  getTextBounds(prgTitle, &tw, &th);
  x=wh-tw/2, y=1;
  drawText(x,y,1,prgTitle,col_red);   
  // version
  drawText(1,pixelY-8,1,prgVersion,col_red);   
  // FreeHeap
  drawFull(pixelX-5,25,8,20,2,(int)ESP.getFreeHeap(),150000,col_red,col_white);

  if(eeMode!=EE_MODE_OK) {
    fillRect(pixelX-13,pixelY-9,pixelX,pixelY,col_red);
    sprintf(buffer, "%d", eeMode); 
    drawText(pixelX-12,pixelY-8,1,buffer,col_black);  
  }

  draw();
}

void pageStart() {
  pageRefresh=5000;
  pageClear();  
  int wh=pixelX/2, wy=pixelY/2, hp=pixelY/pixelX, ii=0;
  int d=1000/wh;
  for(int i=0;i<wh;i++) {
    drawRect(wh-i,wy-ii,(i*2),(ii*2),col_white);
    ii+=hp;  
    draw();
    delay(d);  
  }
}

void pageEsp() {
  pageRefresh=5000;
  pageClear();

  uint32_t chipid=espChipId();
  snprintf(buffer,20, "%08X",chipid);
  drawText(15,1,1,buffer,col_white);
  
  drawText(1,10,1,"Heap",col_red);
  drawFull(40,10,20,8,2,(int)ESP.getFreeHeap(),150000,col_red,col_white);
  
  drawText(1,20,1,"Sketch",col_red);
  drawFull(40,20,20,8,2,(int)ESP.getSketchSize(),(int)ESP.getFreeSketchSpace(),col_red,col_white);
  
  drawText(1,30,1,"CmdOs",col_red);
  drawText(40,30,1,"Os01",col_white);

  // Simple bar chart
  int a[13];
  for(int i=0;i<12;i++) {
    int v=random(0,21); a[i]=v;
    drawFull(i*5+1,40,5,21,1,v,21,col_black,col_red);
  }
  drawLine(1,61,60,61,col_red);
  
  // line chart
  for(int i=1;i<12;i++) {    
    drawLine((i-1)*5+3,40+(a[i-1]),i*5+3,40+(a[i]),col_white);
  }    
  draw();
}

void pageTime() {
  pageRefresh=1000;
  pageClear();
  drawTime(10,5,fontSize,col_red);
  drawDate(2,20,fontSize,col_red);
  drawLine(5,15,60,15,col_white);
  draw();
  delay(250);
  effectStart(1,64,20,0,-5);
}  

void pageGif() {
  pageRefresh=5000;
  pageClear();
  // This would load and display a random GIF file
  logPrintln(LOG_INFO,"pageGif - would display random gif");
  delay(250);
  int rx=random(-1,2)*5;
  int ry=random(-1,2)*5;
  effectStart(1,64,20,rx,ry);
  delay(1300);
}

static int pageCmdNr=0;
void pageCmd() {
  pageRefresh=30000;
  pageClear();  
  // This would execute a random command file
  pageCmdNr++;
  logPrintln(LOG_INFO,"pageCmd - would execute command file");
}

void displayPageSetup() {
  if(pageCount < 10) {
    pages[pageCount++] = new PageFunc("title",pageTitle,NULL);
    pages[pageCount++] = new PageFunc("esp",pageEsp,NULL);
    pages[pageCount++] = new PageFunc("test",pageStart,NULL);
    pages[pageCount++] = new PageFunc("time",pageTime,NULL);
    pages[pageCount++] = new PageFunc("gif",pageGif,NULL);
    pages[pageCount++] = new PageFunc("cmd",pageCmd,NULL);
  }

  pageStart();  
  pageSet(1); 
}

void displayPageLoop() {
  // Additional page loop logic if needed
}